<!-- Hero Section -->
<section class="hero">
  <div class="container">
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          Protect what matters
          <br>— as soon as today
        </h1>
        <p class="hero-subtitle">
          Apply for term life insurance in minutes, with just a few health questions and no medical exams for policies
          up to $3M.
        </p>
        <div class="hero-actions">
          <button class="btn btn-primary btn-large" (click)="getStarted()">
            Get started
          </button>
        </div>
      </div>
      <div class="hero-image">
        <div class="family-photo">
          <!-- Placeholder for family image - you can replace this with an actual image -->
          <div class="family-placeholder">
            <div class="family-members">
              <div class="family-member mom">👩🏽</div>
              <div class="family-member child">👦🏽</div>
              <div class="family-member dad">👨🏽</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Trust Indicators -->
<section class="trust-indicators">
  <div class="container">
    <div class="trust-content">
      <p class="trust-label">Trusted by families nationwide</p>
      <div class="awards">
        <div class="award">
          <div class="award-badge">⭐⭐⭐⭐⭐</div>
          <p class="award-text">Best Digital Life Insurance</p>
          <p class="award-source">Insurance Weekly</p>
        </div>
        <div class="award">
          <div class="award-badge">🏆</div>
          <p class="award-text">Top Rated Insurance Provider</p>
          <p class="award-source">Consumer Choice</p>
        </div>
        <div class="award">
          <div class="award-badge">🥇</div>
          <p class="award-text">Excellence in Customer Service</p>
          <p class="award-source">Industry Awards</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Statistics -->
<section class="statistics">
  <div class="container">
    <div class="stats-grid">
      <div class="stat-item" *ngFor="let stat of stats">
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-label">{{ stat.label }}</div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section id="features" class="features">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Why choose SureVie?</h2>
      <p class="section-subtitle">
        We've simplified life insurance to give you peace of mind without the complexity
      </p>
    </div>
    <div class="features-grid">
      <div class="feature-card" *ngFor="let feature of features">
        <div class="feature-icon">{{ feature.icon }}</div>
        <h3 class="feature-title">{{ feature.title }}</h3>
        <p class="feature-description">{{ feature.description }}</p>
      </div>
    </div>
  </div>
</section>

<!-- Coverage Control Section -->
<section class="coverage-control">
  <div class="container">
    <div class="coverage-content">
      <div class="coverage-text">
        <h2 class="coverage-title">You're in control of your coverage</h2>
        <p class="coverage-description">
          Life changes, and so should your insurance. Adjust your coverage as your needs evolve —
          whether you're paying down your mortgage, your children become independent, or your financial situation
          changes.
        </p>
        <button class="btn btn-primary" (click)="getQuote()">
          Get My Quote
        </button>
      </div>
      <div class="coverage-visual">
        <div class="coverage-chart">
          <div class="chart-bar" style="height: 80%;">
            <span class="chart-label">$1M</span>
          </div>
          <div class="chart-bar" style="height: 60%;">
            <span class="chart-label">$750K</span>
          </div>
          <div class="chart-bar" style="height: 40%;">
            <span class="chart-label">$500K</span>
          </div>
          <div class="chart-bar" style="height: 25%;">
            <span class="chart-label">$250K</span>
          </div>
        </div>
        <p class="chart-note">Adjust your coverage as your life changes</p>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="faq">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">Smart questions you may be asking</h2>
    </div>
    <div class="faq-list">
      <div class="faq-item" *ngFor="let faq of faqs; let i = index" [class.active]="faq.isOpen">
        <button class="faq-question" (click)="toggleFaq(i)">
          <span>{{ faq.question }}</span>
          <span class="faq-toggle" [class.rotated]="faq.isOpen">+</span>
        </button>
        <div class="faq-answer" [class.open]="faq.isOpen">
          <p>{{ faq.answer }}</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Final CTA Section -->
<section class="final-cta">
  <div class="container">
    <div class="cta-content">
      <div class="cta-text">
        <h2 class="cta-title">Security and peace of mind for your family</h2>
        <p class="cta-description">
          Don't leave your family's financial security to chance. With SureVie life insurance,
          you can provide them with the stability they need to stay in their home, finish school,
          and build the future you've always envisioned for them.
        </p>
        <button class="btn btn-primary btn-large" (click)="getStarted()">
          Protect My Family
        </button>
      </div>
      <div class="cta-image">
        <div class="family-image">
          <span class="family-icon">👨‍👩‍👧‍👦</span>
          <div class="protection-shield">🛡️</div>
        </div>
      </div>
    </div>
  </div>
</section>