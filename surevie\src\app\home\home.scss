// Vermeg-inspired color palette
$primary-red: #e31e24;
$dark-gray: #333333;
$light-gray: #f8f9fa;
$white: #ffffff;
$border-gray: #e0e0e0;
$text-gray: #666666;
$background-light: #fafbfc;

// Common styles
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.btn {
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  font-family: inherit;

  &.btn-large {
    padding: 16px 32px;
    font-size: 18px;
  }

  &.btn-primary {
    background-color: $primary-red;
    color: $white;
    border-color: $primary-red;

    &:hover {
      background-color: darken($primary-red, 10%);
      border-color: darken($primary-red, 10%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba($primary-red, 0.3);
    }
  }

  &.btn-outline {
    background-color: transparent;
    color: $dark-gray;
    border-color: $border-gray;

    &:hover {
      background-color: $light-gray;
      border-color: $dark-gray;
      transform: translateY(-2px);
    }
  }
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 42px;
    font-weight: 700;
    color: $dark-gray;
    margin-bottom: 16px;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 32px;
    }
  }

  .section-subtitle {
    font-size: 20px;
    color: $text-gray;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;

    @media (max-width: 768px) {
      font-size: 18px;
    }
  }
}

// Hero Section
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #b8d4e3 0%, #a8c8d8 100%);
  overflow: hidden;



  .container {
    position: relative;
    z-index: 2;
  }

  .hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    min-height: 80vh;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }

  .hero-text {
    .hero-title {
      font-size: 56px;
      font-weight: 800;
      color: $dark-gray;
      line-height: 1.1;
      margin-bottom: 24px;

      .highlight {
        color: $primary-red;
      }

      @media (max-width: 768px) {
        font-size: 40px;
      }

      @media (max-width: 480px) {
        font-size: 32px;
      }
    }

    .hero-subtitle {
      font-size: 20px;
      color: $text-gray;
      line-height: 1.6;
      margin-bottom: 40px;
      max-width: 480px;
      font-weight: 400;

      @media (max-width: 768px) {
        font-size: 18px;
        max-width: none;
      }
    }

    .hero-actions {
      display: flex;
      gap: 20px;
      margin-bottom: 24px;

      @media (max-width: 480px) {
        flex-direction: column;
        align-items: center;
      }
    }

    .hero-note {
      font-size: 16px;
      color: $text-gray;
      font-weight: 500;
    }
  }

  .hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;

    .family-photo {
      position: relative;
      max-width: 100%;
      height: auto;

      .family-img {
        width: 100%;
        max-width: 500px;
        height: auto;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.02);
        }

        @media (max-width: 768px) {
          max-width: 400px;
        }

        @media (max-width: 480px) {
          max-width: 300px;
        }
      }
    }
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }
}

// Trust Indicators
.trust-indicators {
  padding: 60px 0;
  background-color: $background-light;

  .trust-content {
    text-align: center;

    .trust-label {
      font-size: 18px;
      color: $text-gray;
      margin-bottom: 40px;
      font-weight: 500;
    }

    .awards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 30px;
      max-width: 800px;
      margin: 0 auto;

      .award {
        background: $white;
        padding: 30px 20px;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease;

        &:hover {
          transform: translateY(-5px);
        }

        .award-badge {
          font-size: 32px;
          margin-bottom: 16px;
        }

        .award-text {
          font-size: 16px;
          font-weight: 600;
          color: $dark-gray;
          margin-bottom: 8px;
        }

        .award-source {
          font-size: 14px;
          color: $text-gray;
        }
      }
    }
  }
}

// Statistics
.statistics {
  padding: 80px 0;
  background-color: $dark-gray;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    text-align: center;

    .stat-item {
      .stat-value {
        font-size: 48px;
        font-weight: 800;
        color: $primary-red;
        margin-bottom: 8px;
        line-height: 1;

        @media (max-width: 768px) {
          font-size: 36px;
        }
      }

      .stat-label {
        font-size: 18px;
        color: $white;
        font-weight: 500;
      }
    }
  }
}

// Features Section
.features {
  padding: 100px 0;
  background-color: $white;

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;

    .feature-card {
      text-align: center;
      padding: 40px 20px;
      border-radius: 16px;
      transition: all 0.3s ease;
      border: 2px solid transparent;

      &:hover {
        border-color: $primary-red;
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba($primary-red, 0.1);
      }

      .feature-icon {
        font-size: 64px;
        margin-bottom: 24px;
        display: block;
      }

      .feature-title {
        font-size: 24px;
        font-weight: 700;
        color: $dark-gray;
        margin-bottom: 16px;
      }

      .feature-description {
        font-size: 16px;
        color: $text-gray;
        line-height: 1.6;
      }
    }
  }
}

// Coverage Control Section
.coverage-control {
  padding: 100px 0;
  background-color: $background-light;

  .coverage-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }

    .coverage-text {
      .coverage-title {
        font-size: 42px;
        font-weight: 700;
        color: $dark-gray;
        margin-bottom: 24px;
        line-height: 1.2;

        @media (max-width: 768px) {
          font-size: 32px;
        }
      }

      .coverage-description {
        font-size: 20px;
        color: $text-gray;
        line-height: 1.6;
        margin-bottom: 32px;

        @media (max-width: 768px) {
          font-size: 18px;
        }
      }
    }

    .coverage-visual {
      display: flex;
      flex-direction: column;
      align-items: center;

      .coverage-chart {
        display: flex;
        align-items: end;
        gap: 20px;
        height: 200px;
        margin-bottom: 20px;

        .chart-bar {
          width: 60px;
          background: linear-gradient(to top, $primary-red, lighten($primary-red, 20%));
          border-radius: 8px 8px 0 0;
          position: relative;
          transition: all 0.3s ease;

          &:hover {
            transform: scale(1.05);
          }

          .chart-label {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 14px;
            font-weight: 600;
            color: $dark-gray;
            white-space: nowrap;
          }
        }
      }

      .chart-note {
        font-size: 16px;
        color: $text-gray;
        text-align: center;
        font-style: italic;
      }
    }
  }
}

// FAQ Section
.faq {
  padding: 100px 0;
  background-color: $white;

  .faq-list {
    max-width: 800px;
    margin: 0 auto;

    .faq-item {
      border-bottom: 1px solid $border-gray;
      margin-bottom: 0;

      &:last-child {
        border-bottom: none;
      }

      .faq-question {
        width: 100%;
        padding: 24px 0;
        background: none;
        border: none;
        text-align: left;
        font-size: 20px;
        font-weight: 600;
        color: $dark-gray;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: color 0.3s ease;

        &:hover {
          color: $primary-red;
        }

        .faq-toggle {
          font-size: 24px;
          font-weight: 300;
          transition: transform 0.3s ease;
          color: $primary-red;

          &.rotated {
            transform: rotate(45deg);
          }
        }
      }

      .faq-answer {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease, padding 0.3s ease;

        &.open {
          max-height: 200px;
          padding-bottom: 24px;
        }

        p {
          font-size: 16px;
          color: $text-gray;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
}

// Final CTA Section
.final-cta {
  padding: 100px 0;
  background: linear-gradient(135deg, $primary-red 0%, darken($primary-red, 15%) 100%);
  color: $white;

  .cta-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;

    @media (max-width: 968px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }

    .cta-text {
      .cta-title {
        font-size: 42px;
        font-weight: 700;
        color: $white;
        margin-bottom: 24px;
        line-height: 1.2;

        @media (max-width: 768px) {
          font-size: 32px;
        }
      }

      .cta-description {
        font-size: 20px;
        color: rgba($white, 0.9);
        line-height: 1.6;
        margin-bottom: 32px;

        @media (max-width: 768px) {
          font-size: 18px;
        }
      }

      .btn-primary {
        background-color: $white;
        color: $primary-red;
        border-color: $white;

        &:hover {
          background-color: $light-gray;
          border-color: $light-gray;
          color: $primary-red;
        }
      }
    }

    .cta-image {
      display: flex;
      justify-content: center;
      align-items: center;

      .family-image {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .family-icon {
          font-size: 120px;
          opacity: 0.9;
        }

        .protection-shield {
          position: absolute;
          top: -20px;
          right: -20px;
          font-size: 60px;
          background: rgba($white, 0.2);
          border-radius: 50%;
          padding: 10px;
          animation: pulse 2s infinite;
        }
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}